<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>影响者发现平台 - 智能搜索</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #ff6b35;
            --primary-dark: #e55a2b;
            --secondary: #2d3748;
            --accent: #ffd23f;
            --accent-dark: #f4c430;
            --surface: #ffffff;
            --surface-alt: #f8fafc;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --border: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #fafbfc;
            background-image:
                radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 210, 63, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(45, 55, 72, 0.08) 0%, transparent 50%);
            color: var(--text-primary);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* 动态背景粒子效果 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, rgba(255, 107, 53, 0.3), transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255, 210, 63, 0.4), transparent),
                radial-gradient(1px 1px at 90px 40px, rgba(45, 55, 72, 0.2), transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255, 107, 53, 0.2), transparent);
            background-repeat: repeat;
            background-size: 150px 100px;
            animation: float 20s ease-in-out infinite;
            z-index: -1;
            opacity: 0.6;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-10px) rotate(1deg); }
            66% { transform: translateY(5px) rotate(-1deg); }
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            padding: 32px 24px;
        }

        /* 头部样式 - 不对称设计 */
        .header-section {
            background: var(--surface);
            border-radius: 24px 8px 24px 8px;
            padding: 40px 32px 32px;
            margin-bottom: 40px;
            box-shadow: var(--shadow-lg);
            border: 3px solid var(--accent);
            position: relative;
            overflow: hidden;
        }

        .header-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, var(--accent) 0%, transparent 70%);
            opacity: 0.1;
            border-radius: 50%;
        }

        .header-title {
            font-size: 32px;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
        }

        .header-title::after {
            content: '🎯';
            font-size: 24px;
            margin-left: auto;
        }

        .header-subtitle {
            color: var(--text-secondary);
            font-size: 18px;
            margin-bottom: 24px;
            font-weight: 400;
            max-width: 600px;
        }

        .saved-filters-section {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
            position: relative;
        }

        /* 自定义下拉框样式 */
        .custom-select-wrapper {
            position: relative;
            min-width: 280px;
        }

        .custom-select {
            appearance: none;
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-alt) 100%);
            border: 3px solid var(--accent);
            border-radius: 25px 8px 25px 8px;
            padding: 16px 50px 16px 24px;
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
            width: 100%;
        }

        .custom-select:hover {
            border-color: var(--primary);
            transform: translateY(-2px) rotate(-1deg);
            box-shadow: var(--shadow-lg);
        }

        .custom-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.2), var(--shadow-xl);
            transform: translateY(-3px) scale(1.02);
        }

        .custom-select-arrow {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            font-size: 18px;
            color: var(--primary);
            transition: transform 0.3s ease;
        }

        .custom-select:focus + .custom-select-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .btn-save {
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 20px;
            padding: 14px 28px;
            color: white;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .btn-save::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-save:hover::before {
            left: 100%;
        }

        .btn-save:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: var(--shadow-xl);
        }

        /* 主要内容区域 - 不对称布局 */
        .main-content {
            display: grid;
            grid-template-columns: 380px 1fr;
            gap: 32px;
            align-items: start;
        }

        /* 搜索区域 - 有机形状 */
        .search-section {
            background: var(--surface);
            border-radius: 24px 8px 24px 8px;
            padding: 32px 28px;
            box-shadow: var(--shadow-lg);
            border: 2px solid var(--accent);
            position: sticky;
            top: 32px;
            transform: rotate(-0.3deg);
            transition: transform 0.3s ease;
        }

        .search-section:hover {
            transform: rotate(0deg) scale(1.01);
        }

        .search-title {
            font-size: 22px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .search-title::before {
            content: '';
            position: absolute;
            left: -8px;
            top: -8px;
            width: 4px;
            height: calc(100% + 16px);
            background: linear-gradient(180deg, var(--primary) 0%, var(--accent) 100%);
            border-radius: 2px;
        }

        .search-input-group {
            position: relative;
            margin-bottom: 28px;
        }

        .search-input {
            width: 100%;
            padding: 18px 24px 18px 56px;
            border: 3px solid var(--border);
            border-radius: 20px 8px 20px 8px;
            font-size: 16px;
            background: var(--surface-alt);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.15);
            transform: translateY(-2px);
            background: var(--surface);
        }

        .search-input::placeholder {
            color: var(--text-muted);
            font-weight: 400;
        }

        .search-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary);
            font-size: 20px;
            transition: all 0.3s ease;
        }

        .search-input:focus + .search-icon {
            color: var(--primary-dark);
            transform: translateY(-50%) scale(1.1);
        }

        /* 平台选择 - 创意布局 */
        .platform-section {
            margin-bottom: 32px;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .platform-card {
            background: var(--surface);
            border: 3px solid var(--border);
            border-radius: 20px 8px 20px 8px;
            padding: 20px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            will-change: transform;
        }

        .platform-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, var(--accent) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .platform-card:hover::before {
            opacity: 0.1;
        }

        .platform-card:hover {
            border-color: var(--primary);
            transform: translateY(-2px) rotate(0.3deg);
            box-shadow: var(--shadow-lg);
        }

        .platform-card.active {
            border-color: var(--primary);
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 210, 63, 0.1) 100%);
            transform: translateY(-2px) scale(1.05);
        }

        .platform-card.active::after {
            content: '✓';
            position: absolute;
            top: 8px;
            right: 12px;
            color: var(--primary);
            font-weight: bold;
            font-size: 16px;
        }

        .platform-icon {
            width: 36px;
            height: 36px;
            margin-bottom: 12px;
            border-radius: 12px;
            transition: transform 0.3s ease;
        }

        .platform-card:hover .platform-icon {
            transform: scale(1.1) rotate(-5deg);
        }

        .platform-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-top: 8px;
        }

        .platform-radio {
            position: absolute;
            opacity: 0;
        }

        /* 快速开关 */
        .quick-switches {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .switch-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border-radius: 12px;
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .switch-item:hover {
            border-color: var(--primary-light);
        }

        .switch-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* 自定义开关 */
        .custom-switch {
            position: relative;
            width: 48px;
            height: 24px;
        }

        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(24px);
        }

        /* 狩猎按钮 */
        .hunt-button {
            width: 100%;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 50%, var(--accent) 100%);
            border: none;
            border-radius: 25px 8px 25px 8px;
            padding: 18px 24px;
            color: white;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .hunt-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .hunt-button:hover::before {
            left: 100%;
        }

        .hunt-button:hover {
            transform: translateY(-4px) scale(1.05) rotate(-1deg);
            box-shadow: var(--shadow-xl);
        }

        .hunt-button:active {
            transform: translateY(-2px) scale(1.02);
        }

        .hunt-text {
            font-size: 16px;
            font-weight: 700;
        }

        .hunt-icon {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .hunt-button:hover .hunt-icon {
            transform: rotate(15deg) scale(1.2);
        }

        /* 快捷筛选按钮 */
        .quick-filters {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-filter-btn {
            background: var(--surface-alt);
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 10px 16px;
            font-size: 13px;
            font-weight: 500;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
        }

        .quick-filter-btn:hover {
            border-color: var(--accent);
            background: var(--surface);
            transform: translateX(4px);
        }

        .quick-filter-btn.active {
            border-color: var(--primary);
            background: rgba(255, 107, 53, 0.1);
            color: var(--primary);
        }

        /* 状态反馈样式 */
        .form-group.error .form-select,
        .form-group.error .form-control {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-group.success .form-select,
        .form-group.success .form-control {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }

        .form-group.error .error-message {
            display: block;
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: inherit;
            z-index: 10;
        }

        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 成功提示 */
        .success-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .success-toast.show {
            transform: translateX(0);
        }

        /* 无障碍支持 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* 键盘导航焦点样式 */
        .platform-card:focus,
        .quick-filter-btn:focus,
        .hunt-button:focus {
            outline: 3px solid var(--primary);
            outline-offset: 2px;
        }

        /* 过滤器区域 - 创意设计 */
        .filters-section {
            background: var(--surface);
            border-radius: 8px 24px 8px 24px;
            padding: 32px 28px;
            box-shadow: var(--shadow-lg);
            border: 2px solid var(--border);
            position: relative;
            transform: rotate(0.2deg);
            transition: transform 0.3s ease;
        }

        .filters-section:hover {
            transform: rotate(0deg);
        }

        .filters-section::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            background: linear-gradient(45deg, var(--accent), var(--primary));
            border-radius: 8px 32px 8px 32px;
            z-index: -1;
            opacity: 0.1;
        }

        .filters-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 28px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .filters-title::after {
            content: '🔍';
            font-size: 20px;
            margin-left: auto;
        }

        .filter-accordion {
            border: none;
        }

        .accordion-item {
            border: 2px solid var(--border-color);
            border-radius: 12px !important;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .accordion-header {
            border: none;
        }

        .accordion-button {
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-alt) 100%);
            border: none;
            padding: 22px 28px;
            font-weight: 700;
            color: var(--text-primary);
            font-size: 16px;
            border-radius: 20px 8px 20px 8px !important;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .accordion-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
            transition: left 0.6s;
        }

        .accordion-button:hover::before {
            left: 100%;
        }

        .accordion-button:hover {
            transform: translateX(5px) rotate(-0.5deg);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent);
        }

        .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 210, 63, 0.1) 100%);
            color: var(--primary);
            box-shadow: var(--shadow-md);
            transform: translateX(8px) scale(1.02);
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.2);
            outline: none;
        }

        .accordion-body {
            padding: 24px;
            background: white;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: block;
        }

        /* 表单元素重新设计 */
        .form-select, .form-control {
            appearance: none;
            border: 3px solid var(--border);
            border-radius: 20px 6px 20px 6px;
            padding: 14px 40px 14px 18px;
            font-size: 14px;
            font-weight: 500;
            background: linear-gradient(135deg, var(--surface) 0%, var(--surface-alt) 100%);
            color: var(--text-primary);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-sm);
            position: relative;
        }

        .form-select:hover, .form-control:hover {
            border-color: var(--accent);
            transform: translateY(-1px) rotate(-0.5deg);
            box-shadow: var(--shadow-md);
        }

        .form-select:focus, .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.15), var(--shadow-lg);
            transform: translateY(-2px) scale(1.02);
            background: var(--surface);
        }

        /* 为select添加自定义箭头 */
        .form-group {
            position: relative;
        }

        .form-group .form-select::after {
            content: '▼';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            color: var(--primary);
            font-size: 12px;
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .range-inputs .form-select {
            flex: 1;
        }

        .range-separator {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .search-section {
                position: static;
                transform: none; /* 移动端移除旋转 */
            }

            .filters-section {
                transform: none; /* 移动端移除旋转 */
            }

            .platform-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .filter-grid {
                grid-template-columns: 1fr;
            }

            .container-fluid {
                padding: 16px;
            }

            .header-section,
            .search-section,
            .filters-section {
                padding: 20px;
            }

            /* 移动端简化动画 */
            .platform-card:hover,
            .form-select:hover,
            .form-control:hover {
                transform: none;
            }

            .accordion-button:hover {
                transform: none;
            }

            /* 移动端禁用背景动画 */
            body::before {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .platform-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .saved-filters-section {
                flex-direction: column;
                align-items: stretch;
            }

            .saved-filters-section select {
                min-width: auto;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 加载状态 */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 头部区域 -->
        <div class="header-section fade-in-up">
            <div class="header-title">
                <i class="fas fa-crosshairs"></i>
                影响者猎手
            </div>
            <div class="header-subtitle">
                在数字海洋中精准定位那些能为您品牌发声的完美创作者 ✨
            </div>
            <div class="saved-filters-section">
                <div class="custom-select-wrapper">
                    <select class="custom-select" id="saved-filters" name="saved-filters">
                        <option value="">🎯 选择预设方案...</option>
                        <option value="filter1">🚀 科技博主 - 高参与</option>
                        <option value="filter2">💄 美妆达人 - 年轻女性</option>
                        <option value="filter3">🍜 美食探店 - 本地生活</option>
                        <option value="filter4">🏃‍♀️ 健身教练 - 运动爱好者</option>
                        <option value="filter5">🎮 游戏主播 - 电竞圈层</option>
                        <option value="filter6">📚 知识分享 - 教育领域</option>
                    </select>
                    <i class="fas fa-chevron-down custom-select-arrow"></i>
                </div>
                <button type="button" class="btn-save">
                    <i class="fas fa-bookmark"></i>
                    收藏战术
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧搜索区域 -->
            <div class="search-section fade-in-up">
                <div class="search-title">
                    <i class="fas fa-radar"></i>
                    雷达搜索
                </div>

                <!-- 搜索输入 -->
                <div class="search-input-group">
                    <input type="text" class="search-input" id="search" name="search"
                           placeholder="输入@用户名、邮箱或关键词..."
                           aria-label="搜索创作者"
                           aria-describedby="search-help">
                    <i class="fas fa-search search-icon" aria-hidden="true"></i>
                    <div id="search-help" class="sr-only">输入创作者的用户名、邮箱地址或相关关键词进行搜索</div>
                </div>

                <!-- 平台选择 -->
                <div class="platform-section">
                    <h6 class="mb-3" style="font-weight: 700; color: var(--text-primary); font-size: 16px;">🎯 锁定战场</h6>
                    <div class="platform-grid">
                        <div class="platform-card active" onclick="selectPlatform('all')">
                            <input type="radio" class="platform-radio" id="all" name="platform" value="all" checked>
                            <div class="platform-icon" style="background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-globe" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">全域搜索</div>
                        </div>
                        <div class="platform-card" onclick="selectPlatform('instagram')">
                            <input type="radio" class="platform-radio" id="instagram" name="platform" value="instagram">
                            <div class="platform-icon" style="background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-instagram" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">Instagram</div>
                        </div>
                        <div class="platform-card" onclick="selectPlatform('youtube')">
                            <input type="radio" class="platform-radio" id="youtube" name="platform" value="youtube">
                            <div class="platform-icon" style="background: #FF0000; display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-youtube" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">YouTube</div>
                        </div>
                        <div class="platform-card" onclick="selectPlatform('tiktok')">
                            <input type="radio" class="platform-radio" id="tiktok" name="platform" value="tiktok">
                            <div class="platform-icon" style="background: #000000; display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-tiktok" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">TikTok</div>
                        </div>
                    </div>
                </div>

                <!-- 快速开关 -->
                <div class="quick-switches">
                    <div class="switch-item">
                        <span class="switch-label">🔍 仅显示有邮箱的创作者</span>
                        <label class="custom-switch">
                            <input type="checkbox" id="email-available" name="email-available">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="switch-item">
                        <span class="switch-label">👻 隐藏已收藏的目标</span>
                        <label class="custom-switch">
                            <input type="checkbox" id="hide-saved-profiles" name="hide-saved-profiles">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- 快捷筛选 -->
                <div style="margin-top: 24px;">
                    <h6 style="font-weight: 600; color: var(--text-primary); margin-bottom: 12px;">⚡ 快捷筛选</h6>
                    <div class="quick-filters">
                        <button type="button" class="quick-filter-btn" data-filter="micro"
                                aria-label="快速筛选微影响者" tabindex="0">
                            🌱 微影响者 (1K-10K)
                        </button>
                        <button type="button" class="quick-filter-btn" data-filter="macro"
                                aria-label="快速筛选头部网红" tabindex="0">
                            🔥 头部网红 (100K+)
                        </button>
                        <button type="button" class="quick-filter-btn" data-filter="high-engagement"
                                aria-label="快速筛选高参与度创作者" tabindex="0">
                            ⚡ 高参与度 (5%+)
                        </button>
                    </div>
                </div>

                <!-- 启动搜索按钮 -->
                <div style="margin-top: 24px;">
                    <button type="button" class="hunt-button">
                        <span class="hunt-text">🎯 开始搜索</span>
                        <span class="hunt-icon">🚀</span>
                    </button>
                </div>
            </div>

            <!-- 右侧过滤器区域 -->
            <div class="filters-section fade-in-up">
                <div class="filters-title">
                    <i class="fas fa-magic"></i>
                    精准制导系统
                </div>

                <div class="accordion filter-accordion" id="filterAccordion">
                    <!-- 人口统计过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="demographicsHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#demographicsCollapse" aria-expanded="true"
                                    aria-controls="demographicsCollapse">
                                <i class="fas fa-globe-americas me-2"></i>
                                🌍 目标画像定位
                            </button>
                        </h2>
                        <div id="demographicsCollapse" class="accordion-collapse collapse show"
                             aria-labelledby="demographicsHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="location">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            位置
                                        </label>
                                        <select class="form-select" id="location" name="location">
                                            <option value="any">🌍 全球</option>
                                            <option value="usa">🇺🇸 美国</option>
                                            <option value="uk">🇬🇧 英国</option>
                                            <option value="canada">🇨🇦 加拿大</option>
                                            <option value="china">🇨🇳 中国</option>
                                            <option value="japan">🇯🇵 日本</option>
                                            <option value="korea">🇰🇷 韩国</option>
                                            <option value="germany">🇩🇪 德国</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="gender">
                                            <i class="fas fa-venus-mars me-1"></i>
                                            性别
                                        </label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="any">🎭 性别不限</option>
                                            <option value="male">👨 男性创作者</option>
                                            <option value="female">👩 女性创作者</option>
                                            <option value="other">🌈 多元性别</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="age">
                                            <i class="fas fa-birthday-cake me-1"></i>
                                            年龄段
                                        </label>
                                        <select class="form-select" id="age" name="age">
                                            <option value="any">🎂 年龄不限</option>
                                            <option value="13-17">🧒 13-17岁</option>
                                            <option value="18-24">🎓 18-24岁</option>
                                            <option value="25-34">💼 25-34岁</option>
                                            <option value="35-44">🏡 35-44岁</option>
                                            <option value="45+">👑 45岁以上</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="language">
                                            <i class="fas fa-language me-1"></i>
                                            语言
                                        </label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="any">🗣️ 语言通吃</option>
                                            <option value="english">🇺🇸 English 英语霸主</option>
                                            <option value="chinese">🇨🇳 中文 华语圈层</option>
                                            <option value="spanish">🇪🇸 Español 西语世界</option>
                                            <option value="french">🇫🇷 Français 法语优雅</option>
                                            <option value="japanese">🇯🇵 日本語 和风文化</option>
                                            <option value="korean">🇰🇷 한국어 韩流力量</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 表现数据过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="performanceHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#performanceCollapse" aria-expanded="false"
                                    aria-controls="performanceCollapse">
                                <i class="fas fa-rocket me-2"></i>
                                🚀 影响力指数
                            </button>
                        </h2>
                        <div id="performanceCollapse" class="accordion-collapse collapse"
                             aria-labelledby="performanceHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-users me-1"></i>
                                            关注者数量
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="followers-from" name="followers-from">
                                                <option value="">最少粉丝数</option>
                                                <option value="1000">1K 新手创作者</option>
                                                <option value="5000">5K 成长期</option>
                                                <option value="10000">10K 小有名气</option>
                                                <option value="50000">50K 网红级别</option>
                                                <option value="100000">100K 头部创作者</option>
                                                <option value="500000">500K 超级大V</option>
                                                <option value="1000000">1M+ 顶级影响者</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="followers-to" name="followers-to">
                                                <option value="">🏆 最大军团</option>
                                                <option value="10000">⭐ 小有名气 (10K)</option>
                                                <option value="50000">🔥 网红级别 (50K)</option>
                                                <option value="100000">💎 头部玩家 (100K)</option>
                                                <option value="500000">👑 超级大V (500K)</option>
                                                <option value="1000000">🌟 百万军团 (1M)</option>
                                                <option value="5000000">🚀 千万帝国 (5M)</option>
                                                <option value="10000000">🌍 全球巨星 (10M+)</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-trending-up me-1"></i>
                                            关注者增长
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="growth" name="growth">
                                                <option value="">增长数</option>
                                                <option value="100">100+</option>
                                                <option value="500">500+</option>
                                                <option value="1000">1K+</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                            <span class="range-separator">/</span>
                                            <select class="form-select" id="period" name="period">
                                                <option value="">时间段</option>
                                                <option value="week">每周</option>
                                                <option value="month">每月</option>
                                                <option value="quarter">每季度</option>
                                                <option value="year">每年</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="engagement-rate">
                                            <i class="fas fa-heart me-1"></i>
                                            参与率
                                        </label>
                                        <select class="form-select" id="engagement-rate" name="engagement-rate">
                                            <option value="any">💫 参与度不限</option>
                                            <option value="low">😴 佛系互动 (0-2%)</option>
                                            <option value="medium">👍 正常活跃 (2-5%)</option>
                                            <option value="high">🔥 高度粘性 (5-10%)</option>
                                            <option value="very-high">⚡ 超级磁场 (10%+)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 参与度详细数据 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="engagementHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#engagementCollapse" aria-expanded="false"
                                    aria-controls="engagementCollapse">
                                <i class="fas fa-fire me-2"></i>
                                🔥 热度分析仪
                            </button>
                        </h2>
                        <div id="engagementCollapse" class="accordion-collapse collapse"
                             aria-labelledby="engagementHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-heart me-1"></i>
                                            点赞增长
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="likes-growth" name="likes-growth">
                                                <option value="">增长数</option>
                                                <option value="50">50+</option>
                                                <option value="100">100+</option>
                                                <option value="500">500+</option>
                                                <option value="1000">1K+</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                            <span class="range-separator">/</span>
                                            <select class="form-select" id="likes-period" name="likes-period">
                                                <option value="">时间段</option>
                                                <option value="week">每周</option>
                                                <option value="month">每月</option>
                                                <option value="quarter">每季度</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-eye me-1"></i>
                                            浏览量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="views-from" name="views-from">
                                                <option value="">最少</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="views-to" name="views-to">
                                                <option value="">最多</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                                <option value="500000">500K</option>
                                                <option value="1000000">1M+</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-share me-1"></i>
                                            分享量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="shares-from" name="shares-from">
                                                <option value="">最少</option>
                                                <option value="10">10</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="shares-to" name="shares-to">
                                                <option value="">最多</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K</option>
                                                <option value="10000">10K+</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-bookmark me-1"></i>
                                            收藏量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="saves-from" name="saves-from">
                                                <option value="">最少</option>
                                                <option value="5">5</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="saves-to" name="saves-to">
                                                <option value="">最多</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="contentHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#contentCollapse" aria-expanded="false"
                                    aria-controls="contentCollapse">
                                <i class="fas fa-palette me-2"></i>
                                🎨 内容DNA解析
                            </button>
                        </h2>
                        <div id="contentCollapse" class="accordion-collapse collapse"
                             aria-labelledby="contentHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="topics">
                                            <i class="fas fa-tags me-1"></i>
                                            内容主题
                                        </label>
                                        <select class="form-select" id="topics" name="topics">
                                            <option value="any">🎨 内容不限</option>
                                            <option value="tech">🤖 科技极客 - 数码前沿</option>
                                            <option value="lifestyle">✨ 生活美学 - 品质追求</option>
                                            <option value="fashion">💄 时尚潮流 - 美妆种草</option>
                                            <option value="food">🍜 美食探店 - 味蕾征服</option>
                                            <option value="travel">🌍 旅行达人 - 世界收集者</option>
                                            <option value="fitness">💪 健身教练 - 身材雕塑师</option>
                                            <option value="education">📚 知识博主 - 智慧传播者</option>
                                            <option value="entertainment">🎭 娱乐搞笑 - 快乐制造机</option>
                                            <option value="gaming">🎮 游戏主播 - 电竞王者</option>
                                            <option value="music">🎵 音乐创作 - 旋律魔法师</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="hashtags">
                                            <i class="fas fa-hashtag me-1"></i>
                                            热门标签
                                        </label>
                                        <select class="form-select" id="hashtags" name="hashtags">
                                            <option value="any">任意标签</option>
                                            <option value="#travel">#travel</option>
                                            <option value="#food">#food</option>
                                            <option value="#fashion">#fashion</option>
                                            <option value="#tech">#tech</option>
                                            <option value="#fitness">#fitness</option>
                                            <option value="#beauty">#beauty</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="mentions">
                                            <i class="fas fa-at me-1"></i>
                                            品牌提及
                                        </label>
                                        <input type="text" class="form-control" id="mentions" name="mentions"
                                               placeholder="输入品牌名或@用户名">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="captions">
                                            <i class="fas fa-align-left me-1"></i>
                                            内容类型
                                        </label>
                                        <select class="form-select" id="captions" name="captions">
                                            <option value="any">任意类型</option>
                                            <option value="promo">推广内容</option>
                                            <option value="review">产品评测</option>
                                            <option value="tutorial">教程指南</option>
                                            <option value="lifestyle">生活分享</option>
                                            <option value="unboxing">开箱体验</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户信息过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="accountHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#accountCollapse" aria-expanded="false"
                                    aria-controls="accountCollapse">
                                <i class="fas fa-fingerprint me-2"></i>
                                👤 创作者档案
                            </button>
                        </h2>
                        <div id="accountCollapse" class="accordion-collapse collapse"
                             aria-labelledby="accountHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="bio">
                                            <i class="fas fa-info-circle me-1"></i>
                                            账户类型
                                        </label>
                                        <select class="form-select" id="bio" name="bio">
                                            <option value="any">任意类型</option>
                                            <option value="influencer">个人影响者</option>
                                            <option value="brand">品牌官方</option>
                                            <option value="creator">内容创作者</option>
                                            <option value="celebrity">公众人物</option>
                                            <option value="business">商业账户</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="social">
                                            <i class="fas fa-chart-pulse me-1"></i>
                                            活跃度
                                        </label>
                                        <select class="form-select" id="social" name="social">
                                            <option value="any">任意活跃度</option>
                                            <option value="very-active">非常活跃 (每日发布)</option>
                                            <option value="active">活跃 (每周发布)</option>
                                            <option value="moderate">中等 (每月发布)</option>
                                            <option value="inactive">不活跃 (很少发布)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="last-posted">
                                            <i class="fas fa-clock me-1"></i>
                                            最后发布时间
                                        </label>
                                        <select class="form-select" id="last-posted" name="last-posted">
                                            <option value="any">任意时间</option>
                                            <option value="day">最近24小时</option>
                                            <option value="week">最近一周</option>
                                            <option value="month">最近一月</option>
                                            <option value="quarter">最近三月</option>
                                            <option value="year">最近一年</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="verification">
                                            <i class="fas fa-check-circle me-1"></i>
                                            认证状态
                                        </label>
                                        <select class="form-select" id="verification" name="verification">
                                            <option value="any">任意状态</option>
                                            <option value="verified">已认证</option>
                                            <option value="unverified">未认证</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 平台选择功能 (单选)
        function selectPlatform(platform) {
            // 移除所有平台卡片的active状态
            const allCards = document.querySelectorAll('.platform-card');
            allCards.forEach(card => card.classList.remove('active'));

            // 选中当前平台
            const radio = document.getElementById(platform);
            const card = radio.closest('.platform-card');

            radio.checked = true;
            card.classList.add('active');

            // 无障碍支持：通知屏幕阅读器
            card.setAttribute('aria-selected', 'true');
            allCards.forEach(otherCard => {
                if (otherCard !== card) {
                    otherCard.setAttribute('aria-selected', 'false');
                }
            });
        }

        // 快捷筛选功能
        function applyQuickFilter(filterType) {
            const followersFrom = document.getElementById('followers-from');
            const followersTo = document.getElementById('followers-to');
            const engagementRate = document.getElementById('engagement-rate');

            // 重置之前的选择
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 激活当前按钮
            event.target.classList.add('active');

            switch(filterType) {
                case 'micro':
                    followersFrom.value = '1000';
                    followersTo.value = '10000';
                    break;
                case 'macro':
                    followersFrom.value = '100000';
                    followersTo.value = '';
                    break;
                case 'high-engagement':
                    engagementRate.value = 'high';
                    break;
            }

            showSuccessToast('快捷筛选已应用');
        }

        // 成功提示
        function showSuccessToast(message) {
            const toast = document.createElement('div');
            toast.className = 'success-toast';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 2000);
        }

        // 表单验证
        function validateForm() {
            let isValid = true;
            const searchInput = document.getElementById('search');

            // 清除之前的错误状态
            document.querySelectorAll('.form-group').forEach(group => {
                group.classList.remove('error', 'success');
            });

            // 验证搜索输入（如果有内容）
            if (searchInput.value.trim() && searchInput.value.length < 2) {
                const group = searchInput.closest('.search-input-group');
                if (group) {
                    group.classList.add('error');
                    isValid = false;
                }
            }

            return isValid;
        }

        // 添加动画效果和事件监听
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有表单元素添加焦点效果
            const formElements = document.querySelectorAll('.form-select, .form-control, .search-input');
            formElements.forEach(element => {
                element.addEventListener('focus', function() {
                    if (window.innerWidth > 768) { // 只在桌面端添加动画
                        this.parentElement.style.transform = 'translateY(-2px)';
                    }
                });

                element.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // 快捷筛选按钮事件
            document.querySelectorAll('.quick-filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    applyQuickFilter(this.dataset.filter);
                });

                // 键盘支持
                btn.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        applyQuickFilter(this.dataset.filter);
                    }
                });
            });

            // 搜索按钮功能
            document.querySelector('.hunt-button').addEventListener('click', function() {
                if (validateForm()) {
                    // 显示加载状态
                    this.innerHTML = '<div class="loading-spinner"></div> 搜索中...';
                    this.disabled = true;

                    // 模拟搜索过程
                    setTimeout(() => {
                        this.innerHTML = '<span class="hunt-text">🎯 开始搜索</span><span class="hunt-icon">🚀</span>';
                        this.disabled = false;
                        showSuccessToast('搜索完成！找到 127 个匹配的创作者');
                    }, 2000);
                } else {
                    showSuccessToast('请检查输入内容');
                }
            });

            // 保存过滤器功能
            document.querySelector('.btn-save').addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-check-circle"></i> 已收藏 ✨';
                this.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-bookmark"></i> 收藏战术';
                    this.style.background = 'linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%)';
                }, 2500);

                showSuccessToast('筛选方案已保存');
            });

            // 键盘导航支持
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // ESC键清除所有焦点
                    document.activeElement.blur();
                }
            });
        });
    </script>
</body>
</html>