<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>影响者发现平台 - 智能搜索</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-light: #a5b4fc;
            --primary-dark: #4f46e5;
            --secondary-color: #f1f5f9;
            --accent-color: #06b6d4;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --border-color: #e2e8f0;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-primary);
            line-height: 1.6;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .header-subtitle {
            color: var(--text-secondary);
            font-size: 16px;
            margin-bottom: 20px;
        }

        .saved-filters-section {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        .saved-filters-section select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            background: white;
            color: var(--text-primary);
            min-width: 200px;
            transition: all 0.3s ease;
        }

        .saved-filters-section select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .btn-save {
            background: var(--primary-color);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-save:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--card-shadow-hover);
        }

        /* 主要内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            align-items: start;
        }

        /* 搜索区域 */
        .search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 20px;
        }

        .search-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 16px 20px 16px 50px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: white;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 18px;
        }

        /* 平台选择 */
        .platform-section {
            margin-bottom: 24px;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
        }

        .platform-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .platform-card:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--card-shadow);
        }

        .platform-card.active {
            border-color: var(--primary-color);
            background: rgba(99, 102, 241, 0.05);
        }

        .platform-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 8px;
            border-radius: 8px;
        }

        .platform-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        .platform-checkbox {
            position: absolute;
            opacity: 0;
        }

        /* 快速开关 */
        .quick-switches {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .switch-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border-radius: 12px;
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .switch-item:hover {
            border-color: var(--primary-light);
        }

        .switch-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* 自定义开关 */
        .custom-switch {
            position: relative;
            width: 48px;
            height: 24px;
        }

        .custom-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(24px);
        }

        /* 过滤器区域 */
        .filters-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .filters-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-accordion {
            border: none;
        }

        .accordion-item {
            border: 2px solid var(--border-color);
            border-radius: 12px !important;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .accordion-header {
            border: none;
        }

        .accordion-button {
            background: white;
            border: none;
            padding: 20px 24px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 16px;
            border-radius: 12px !important;
        }

        .accordion-button:not(.collapsed) {
            background: rgba(99, 102, 241, 0.05);
            color: var(--primary-color);
            box-shadow: none;
        }

        .accordion-button:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .accordion-body {
            padding: 24px;
            background: white;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .form-group {
            margin-bottom: 0;
        }

        .form-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            display: block;
        }

        .form-select, .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            background: white;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .range-inputs .form-select {
            flex: 1;
        }

        .range-separator {
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .search-section {
                position: static;
            }

            .platform-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .filter-grid {
                grid-template-columns: 1fr;
            }

            .container-fluid {
                padding: 16px;
            }

            .header-section,
            .search-section,
            .filters-section {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .platform-grid {
                grid-template-columns: 1fr;
            }

            .saved-filters-section {
                flex-direction: column;
                align-items: stretch;
            }

            .saved-filters-section select {
                min-width: auto;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 加载状态 */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 头部区域 -->
        <div class="header-section fade-in-up">
            <div class="header-title">
                <i class="fas fa-search"></i>
                影响者发现平台
            </div>
            <div class="header-subtitle">
                使用智能过滤器找到最适合您品牌的影响者
            </div>
            <div class="saved-filters-section">
                <select id="saved-filters" name="saved-filters">
                    <option value="">选择保存的过滤器</option>
                    <option value="filter1">科技博主 - 高参与率</option>
                    <option value="filter2">生活方式 - 年轻女性</option>
                    <option value="filter3">美食达人 - 本地市场</option>
                </select>
                <button type="button" class="btn-save">
                    <i class="fas fa-save"></i>
                    保存过滤器
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧搜索区域 -->
            <div class="search-section fade-in-up">
                <div class="search-title">
                    <i class="fas fa-filter"></i>
                    快速搜索
                </div>

                <!-- 搜索输入 -->
                <div class="search-input-group">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" id="search" name="search"
                           placeholder="搜索创作者用户名或邮箱...">
                </div>

                <!-- 平台选择 -->
                <div class="platform-section">
                    <h6 class="mb-3" style="font-weight: 600; color: var(--text-primary);">选择平台</h6>
                    <div class="platform-grid">
                        <div class="platform-card" onclick="togglePlatform('instagram')">
                            <input type="checkbox" class="platform-checkbox" id="instagram" name="platform" value="instagram">
                            <div style="background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); width: 32px; height: 32px; border-radius: 8px; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-instagram" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">Instagram</div>
                        </div>
                        <div class="platform-card" onclick="togglePlatform('youtube')">
                            <input type="checkbox" class="platform-checkbox" id="youtube" name="platform" value="youtube">
                            <div style="background: #FF0000; width: 32px; height: 32px; border-radius: 8px; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-youtube" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">YouTube</div>
                        </div>
                        <div class="platform-card" onclick="togglePlatform('tiktok')">
                            <input type="checkbox" class="platform-checkbox" id="tiktok" name="platform" value="tiktok">
                            <div style="background: #000000; width: 32px; height: 32px; border-radius: 8px; margin: 0 auto 8px; display: flex; align-items: center; justify-content: center;">
                                <i class="fab fa-tiktok" style="color: white; font-size: 18px;"></i>
                            </div>
                            <div class="platform-name">TikTok</div>
                        </div>
                    </div>
                </div>

                <!-- 快速开关 -->
                <div class="quick-switches">
                    <div class="switch-item">
                        <span class="switch-label">仅显示有邮箱的创作者</span>
                        <label class="custom-switch">
                            <input type="checkbox" id="email-available" name="email-available">
                            <span class="slider"></span>
                        </label>
                    </div>
                    <div class="switch-item">
                        <span class="switch-label">隐藏已保存的资料</span>
                        <label class="custom-switch">
                            <input type="checkbox" id="hide-saved-profiles" name="hide-saved-profiles">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- 右侧过滤器区域 -->
            <div class="filters-section fade-in-up">
                <div class="filters-title">
                    <i class="fas fa-sliders-h"></i>
                    高级过滤器
                </div>

                <div class="accordion filter-accordion" id="filterAccordion">
                    <!-- 人口统计过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="demographicsHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#demographicsCollapse" aria-expanded="true"
                                    aria-controls="demographicsCollapse">
                                <i class="fas fa-users me-2"></i>
                                人口统计
                            </button>
                        </h2>
                        <div id="demographicsCollapse" class="accordion-collapse collapse show"
                             aria-labelledby="demographicsHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="location">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            位置
                                        </label>
                                        <select class="form-select" id="location" name="location">
                                            <option value="any">任意位置</option>
                                            <option value="usa">美国</option>
                                            <option value="uk">英国</option>
                                            <option value="canada">加拿大</option>
                                            <option value="china">中国</option>
                                            <option value="japan">日本</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="gender">
                                            <i class="fas fa-venus-mars me-1"></i>
                                            性别
                                        </label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="any">任意性别</option>
                                            <option value="male">男性</option>
                                            <option value="female">女性</option>
                                            <option value="other">其他</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="age">
                                            <i class="fas fa-birthday-cake me-1"></i>
                                            年龄段
                                        </label>
                                        <select class="form-select" id="age" name="age">
                                            <option value="any">任意年龄</option>
                                            <option value="13-17">13-17岁</option>
                                            <option value="18-24">18-24岁</option>
                                            <option value="25-34">25-34岁</option>
                                            <option value="35-44">35-44岁</option>
                                            <option value="45+">45岁以上</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="language">
                                            <i class="fas fa-language me-1"></i>
                                            语言
                                        </label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="any">任意语言</option>
                                            <option value="english">英语</option>
                                            <option value="chinese">中文</option>
                                            <option value="spanish">西班牙语</option>
                                            <option value="french">法语</option>
                                            <option value="japanese">日语</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 表现数据过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="performanceHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#performanceCollapse" aria-expanded="false"
                                    aria-controls="performanceCollapse">
                                <i class="fas fa-chart-line me-2"></i>
                                表现数据
                            </button>
                        </h2>
                        <div id="performanceCollapse" class="accordion-collapse collapse"
                             aria-labelledby="performanceHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-users me-1"></i>
                                            关注者数量
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="followers-from" name="followers-from">
                                                <option value="">最少</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                                <option value="500000">500K</option>
                                                <option value="1000000">1M</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="followers-to" name="followers-to">
                                                <option value="">最多</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                                <option value="500000">500K</option>
                                                <option value="1000000">1M</option>
                                                <option value="5000000">5M</option>
                                                <option value="10000000">10M+</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-trending-up me-1"></i>
                                            关注者增长
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="growth" name="growth">
                                                <option value="">增长数</option>
                                                <option value="100">100+</option>
                                                <option value="500">500+</option>
                                                <option value="1000">1K+</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                            <span class="range-separator">/</span>
                                            <select class="form-select" id="period" name="period">
                                                <option value="">时间段</option>
                                                <option value="week">每周</option>
                                                <option value="month">每月</option>
                                                <option value="quarter">每季度</option>
                                                <option value="year">每年</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="engagement-rate">
                                            <i class="fas fa-heart me-1"></i>
                                            参与率
                                        </label>
                                        <select class="form-select" id="engagement-rate" name="engagement-rate">
                                            <option value="any">任意参与率</option>
                                            <option value="low">低 (0-2%)</option>
                                            <option value="medium">中等 (2-5%)</option>
                                            <option value="high">高 (5-10%)</option>
                                            <option value="very-high">极高 (10%+)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 参与度详细数据 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="engagementHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#engagementCollapse" aria-expanded="false"
                                    aria-controls="engagementCollapse">
                                <i class="fas fa-thumbs-up me-2"></i>
                                参与度详情
                            </button>
                        </h2>
                        <div id="engagementCollapse" class="accordion-collapse collapse"
                             aria-labelledby="engagementHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-heart me-1"></i>
                                            点赞增长
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="likes-growth" name="likes-growth">
                                                <option value="">增长数</option>
                                                <option value="50">50+</option>
                                                <option value="100">100+</option>
                                                <option value="500">500+</option>
                                                <option value="1000">1K+</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                            <span class="range-separator">/</span>
                                            <select class="form-select" id="likes-period" name="likes-period">
                                                <option value="">时间段</option>
                                                <option value="week">每周</option>
                                                <option value="month">每月</option>
                                                <option value="quarter">每季度</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-eye me-1"></i>
                                            浏览量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="views-from" name="views-from">
                                                <option value="">最少</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="views-to" name="views-to">
                                                <option value="">最多</option>
                                                <option value="10000">10K</option>
                                                <option value="50000">50K</option>
                                                <option value="100000">100K</option>
                                                <option value="500000">500K</option>
                                                <option value="1000000">1M+</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-share me-1"></i>
                                            分享量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="shares-from" name="shares-from">
                                                <option value="">最少</option>
                                                <option value="10">10</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="shares-to" name="shares-to">
                                                <option value="">最多</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K</option>
                                                <option value="10000">10K+</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i class="fas fa-bookmark me-1"></i>
                                            收藏量范围
                                        </label>
                                        <div class="range-inputs">
                                            <select class="form-select" id="saves-from" name="saves-from">
                                                <option value="">最少</option>
                                                <option value="5">5</option>
                                                <option value="25">25</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                            </select>
                                            <span class="range-separator">至</span>
                                            <select class="form-select" id="saves-to" name="saves-to">
                                                <option value="">最多</option>
                                                <option value="50">50</option>
                                                <option value="100">100</option>
                                                <option value="500">500</option>
                                                <option value="1000">1K</option>
                                                <option value="5000">5K+</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 内容过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="contentHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#contentCollapse" aria-expanded="false"
                                    aria-controls="contentCollapse">
                                <i class="fas fa-hashtag me-2"></i>
                                内容分析
                            </button>
                        </h2>
                        <div id="contentCollapse" class="accordion-collapse collapse"
                             aria-labelledby="contentHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="topics">
                                            <i class="fas fa-tags me-1"></i>
                                            内容主题
                                        </label>
                                        <select class="form-select" id="topics" name="topics">
                                            <option value="any">任意主题</option>
                                            <option value="tech">科技数码</option>
                                            <option value="lifestyle">生活方式</option>
                                            <option value="fashion">时尚美妆</option>
                                            <option value="food">美食料理</option>
                                            <option value="travel">旅行探索</option>
                                            <option value="fitness">健身运动</option>
                                            <option value="education">教育学习</option>
                                            <option value="entertainment">娱乐搞笑</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="hashtags">
                                            <i class="fas fa-hashtag me-1"></i>
                                            热门标签
                                        </label>
                                        <select class="form-select" id="hashtags" name="hashtags">
                                            <option value="any">任意标签</option>
                                            <option value="#travel">#travel</option>
                                            <option value="#food">#food</option>
                                            <option value="#fashion">#fashion</option>
                                            <option value="#tech">#tech</option>
                                            <option value="#fitness">#fitness</option>
                                            <option value="#beauty">#beauty</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="mentions">
                                            <i class="fas fa-at me-1"></i>
                                            品牌提及
                                        </label>
                                        <input type="text" class="form-control" id="mentions" name="mentions"
                                               placeholder="输入品牌名或@用户名">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="captions">
                                            <i class="fas fa-align-left me-1"></i>
                                            内容类型
                                        </label>
                                        <select class="form-select" id="captions" name="captions">
                                            <option value="any">任意类型</option>
                                            <option value="promo">推广内容</option>
                                            <option value="review">产品评测</option>
                                            <option value="tutorial">教程指南</option>
                                            <option value="lifestyle">生活分享</option>
                                            <option value="unboxing">开箱体验</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户信息过滤器 -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="accountHeading">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#accountCollapse" aria-expanded="false"
                                    aria-controls="accountCollapse">
                                <i class="fas fa-user-circle me-2"></i>
                                账户信息
                            </button>
                        </h2>
                        <div id="accountCollapse" class="accordion-collapse collapse"
                             aria-labelledby="accountHeading" data-bs-parent="#filterAccordion">
                            <div class="accordion-body">
                                <div class="filter-grid">
                                    <div class="form-group">
                                        <label class="form-label" for="bio">
                                            <i class="fas fa-info-circle me-1"></i>
                                            账户类型
                                        </label>
                                        <select class="form-select" id="bio" name="bio">
                                            <option value="any">任意类型</option>
                                            <option value="influencer">个人影响者</option>
                                            <option value="brand">品牌官方</option>
                                            <option value="creator">内容创作者</option>
                                            <option value="celebrity">公众人物</option>
                                            <option value="business">商业账户</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="social">
                                            <i class="fas fa-chart-pulse me-1"></i>
                                            活跃度
                                        </label>
                                        <select class="form-select" id="social" name="social">
                                            <option value="any">任意活跃度</option>
                                            <option value="very-active">非常活跃 (每日发布)</option>
                                            <option value="active">活跃 (每周发布)</option>
                                            <option value="moderate">中等 (每月发布)</option>
                                            <option value="inactive">不活跃 (很少发布)</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="last-posted">
                                            <i class="fas fa-clock me-1"></i>
                                            最后发布时间
                                        </label>
                                        <select class="form-select" id="last-posted" name="last-posted">
                                            <option value="any">任意时间</option>
                                            <option value="day">最近24小时</option>
                                            <option value="week">最近一周</option>
                                            <option value="month">最近一月</option>
                                            <option value="quarter">最近三月</option>
                                            <option value="year">最近一年</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label" for="verification">
                                            <i class="fas fa-check-circle me-1"></i>
                                            认证状态
                                        </label>
                                        <select class="form-select" id="verification" name="verification">
                                            <option value="any">任意状态</option>
                                            <option value="verified">已认证</option>
                                            <option value="unverified">未认证</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 平台选择功能
        function togglePlatform(platform) {
            const checkbox = document.getElementById(platform);
            const card = checkbox.closest('.platform-card');

            checkbox.checked = !checkbox.checked;

            if (checkbox.checked) {
                card.classList.add('active');
            } else {
                card.classList.remove('active');
            }
        }

        // 添加动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有表单元素添加焦点效果
            const formElements = document.querySelectorAll('.form-select, .form-control, .search-input');
            formElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateY(-2px)';
                });

                element.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateY(0)';
                });
            });

            // 保存过滤器功能
            document.querySelector('.btn-save').addEventListener('click', function() {
                // 这里可以添加保存逻辑
                this.innerHTML = '<i class="fas fa-check"></i> 已保存';
                this.style.background = 'var(--success-color)';

                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-save"></i> 保存过滤器';
                    this.style.background = 'var(--primary-color)';
                }, 2000);
            });
        });
    </script>
</body>
</html>